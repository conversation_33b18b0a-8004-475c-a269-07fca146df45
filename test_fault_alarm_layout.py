"""
测试故障异常报警页面布局的脚本
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from ui.fault_alarm_system import FaultAlarmSystem

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("故障异常报警页面布局测试")
        self.setGeometry(100, 100, 1280, 900)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加故障报警系统
        self.fault_alarm = FaultAlarmSystem()
        layout.addWidget(self.fault_alarm)

def main():
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("故障异常报警页面布局测试")
    print("请检查以下项目：")
    print("1. 标签和数值是否都完全显示在卡片边界内")
    print("2. 标签是否靠左对齐，数值是否靠右对齐")
    print("3. 标签距离左边缘的距离是否等于数值距离右边缘的距离")
    print("4. 卡片是否正确对齐且不超出页面")
    print("\n按 Ctrl+C 退出测试")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n测试结束")
        sys.exit(0)

if __name__ == "__main__":
    main()
