"""
分析故障异常报警页面卡片布局的脚本
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt, QTimer
from ui.fault_alarm_system import FaultAlarmSystem, StatusCard

class LayoutAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("卡片布局分析器")
        self.setGeometry(100, 100, 1280, 900)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加故障报警系统
        self.fault_alarm = FaultAlarmSystem()
        layout.addWidget(self.fault_alarm)
        
        # 设置定时器来分析布局
        QTimer.singleShot(1000, self.analyze_layout)
    
    def analyze_layout(self):
        """分析卡片布局"""
        print("\n=== 卡片布局分析报告 ===")
        
        # 获取所有卡片
        cards = []
        if hasattr(self.fault_alarm, 'fault_card'):
            cards.append(("故障判断卡片", self.fault_alarm.fault_card))
        if hasattr(self.fault_alarm, 'classical_card'):
            cards.append(("传统分类器卡片", self.fault_alarm.classical_card))
        if hasattr(self.fault_alarm, 'dl_card'):
            cards.append(("深度学习卡片", self.fault_alarm.dl_card))
        
        for card_name, card in cards:
            print(f"\n--- {card_name} ---")
            self.analyze_card(card)
    
    def analyze_card(self, card):
        """分析单个卡片"""
        # 获取卡片尺寸
        card_rect = card.geometry()
        print(f"卡片尺寸: {card_rect.width()} x {card_rect.height()}")
        print(f"卡片位置: ({card_rect.x()}, {card_rect.y()})")
        
        # 获取卡片的布局边距
        layout = card.layout()
        if layout:
            margins = layout.contentsMargins()
            print(f"卡片内边距: 左{margins.left()}, 上{margins.top()}, 右{margins.right()}, 下{margins.bottom()}")
        
        # 分析数据项
        if hasattr(card, 'data_layout'):
            data_layout = card.data_layout
            print(f"数据项数量: {data_layout.count()}")
            
            for i in range(data_layout.count()):
                item = data_layout.itemAt(i)
                if item and item.layout():
                    self.analyze_data_item(item.layout(), i)
    
    def analyze_data_item(self, item_layout, index):
        """分析数据项布局"""
        print(f"  数据项 {index + 1}:")
        
        # 获取布局中的控件
        label_widget = None
        value_widget = None
        
        for i in range(item_layout.count()):
            item = item_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'text'):
                    text = widget.text()
                    if i == 0:  # 第一个是标签
                        label_widget = widget
                        print(f"    标签: '{text}' 宽度={widget.width()}, 位置=({widget.x()}, {widget.y()})")
                    elif i == 2:  # 第三个是数值（中间是拉伸空间）
                        value_widget = widget
                        print(f"    数值: '{text}' 宽度={widget.width()}, 位置=({widget.x()}, {widget.y()})")
        
        # 检查对齐情况
        if label_widget and value_widget:
            card_widget = label_widget.parent()
            while card_widget and not isinstance(card_widget, StatusCard):
                card_widget = card_widget.parent()
            
            if card_widget:
                card_rect = card_widget.geometry()
                label_left_margin = label_widget.x()
                value_right_margin = card_rect.width() - (value_widget.x() + value_widget.width())
                
                print(f"    标签距离左边缘: {label_left_margin}px")
                print(f"    数值距离右边缘: {value_right_margin}px")
                print(f"    是否对称: {'是' if abs(label_left_margin - value_right_margin) <= 2 else '否'}")
                
                # 检查是否超出边界
                if value_widget.x() + value_widget.width() > card_rect.width():
                    print(f"    ⚠️ 数值超出卡片右边界 {(value_widget.x() + value_widget.width()) - card_rect.width()}px")
                else:
                    print(f"    ✅ 数值在卡片边界内")

def main():
    app = QApplication(sys.argv)
    
    # 创建分析器窗口
    analyzer = LayoutAnalyzer()
    analyzer.show()
    
    print("卡片布局分析器已启动...")
    print("分析报告将在1秒后显示")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n分析结束")
        sys.exit(0)

if __name__ == "__main__":
    main()
