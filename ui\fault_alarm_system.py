"""
故障异常报警系统模块
"""

import sys
import random
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QFrame, QGridLayout,
                             QScrollArea, QSizePolicy, QDialog, QTableWidget,
                             QTableWidgetItem, QHeaderView, QLineEdit, QComboBox,
                             QDateEdit, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon, QPalette
from ui.styles import (PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, HIGHLIGHT_COLOR,
                       TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, WARNING_COLOR,
                       ERROR_COLOR, INFO_COLOR)


class StatusCard(QFrame):
    """状态卡片组件"""

    def __init__(self, title, icon, status="normal"):
        super().__init__()
        self.title = title
        self.icon = icon
        self.status = status
        self.init_ui()

    def init_ui(self):
        """初始化卡片界面"""
        # 针对主程序工作区优化：考虑导航栏占用280px，实际可用宽度约1000px
        # 三个卡片需要适应1000px工作区：(1000-20)/3 ≈ 320px每个卡片
        self.setMinimumSize(280, 480)  # 最小宽度
        self.setMaximumWidth(320)  # 调整为320px以适应三个卡片在1000px工作区内
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)  # 水平可扩展，垂直固定
        # 现代工业监测仪表板卡片设计：圆角、微妙阴影、柔和渐变
        self.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8fafb);
                border: 1px solid #e1e8ed;
                border-radius: 16px;
                margin: 6px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f0f6fa);
                border: 1px solid #c7d2fe;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 12, 6, 15)  # 左右内边距相等
        layout.setSpacing(8)  # 减小内部间距

        # 卡片标题和状态 - 现代化设计
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 8)

        # 清晰的标题层级，文字靠左对齐
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-size: 16px;
            font-weight: 700;
            color: #2d3748;
            padding: 6px 0px;
            letter-spacing: 0.5px;
        """)
        title_label.setAlignment(Qt.AlignLeft)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 状态标签
        self.status_label = QLabel()
        self.update_status(self.status)
        header_layout.addWidget(self.status_label)

        layout.addLayout(header_layout)

        # 微妙的分隔线设计 - 现代工业风格
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFixedHeight(1)
        line.setStyleSheet(f"""
            QFrame {{
                background-color: #e2e8f0;
                border: none;
                margin: 8px 0px;
            }}
        """)
        layout.addWidget(line)

        # 数据区域
        self.data_layout = QVBoxLayout()
        self.data_layout.setSpacing(12)  # 减小数据项之间的间距
        self.data_layout.setContentsMargins(2, 8, 2, 8)  # 左右边距相等
        layout.addLayout(self.data_layout)

        layout.addStretch()
    
    def update_status(self, status):
        """更新状态 - 统一的红橙色调警告/故障配色方案"""
        self.status = status
        # 统一的红橙色调配色方案，避免刺眼的亮红色
        normal_color = "#10b981"      # 现代绿色
        warning_color = "#f59e0b"     # 统一的橙色调
        alarm_color = "#ef4444"       # 统一的红橙色调

        if status == "normal":
            self.status_label.setText("运行正常")
            self.status_label.setStyleSheet(f"""
                background-color: {normal_color};
                color: white;
                padding: 8px 16px;
                border-radius: 12px;
                font-weight: 600;
                font-size: 12px;
                border: none;
            """)
        elif status == "warning":
            self.status_label.setText("警告")
            self.status_label.setStyleSheet(f"""
                background-color: {warning_color};
                color: white;
                padding: 8px 16px;
                border-radius: 12px;
                font-weight: 600;
                font-size: 12px;
                border: none;
            """)
        elif status == "alarm":
            self.status_label.setText("故障报警")
            self.status_label.setStyleSheet(f"""
                background-color: {alarm_color};
                color: white;
                padding: 8px 16px;
                border-radius: 12px;
                font-weight: 600;
                font-size: 12px;
                border: none;
            """)
    
    def add_data_item(self, label, value, unit=""):
        """添加数据项 - 现代工业监测风格，清晰的字体层级"""
        item_layout = QHBoxLayout()
        item_layout.setContentsMargins(0, 8, 0, 8)  # 移除左右边距，让标签和数值有更多空间

        label_widget = QLabel(label)
        # 现代化标签设计 - 微妙背景，清晰层级，文字左对齐
        label_widget.setStyleSheet(f"""
            color: #6b7280;
            font-size: 16px;
            font-weight: 500;
            min-height: 28px;
            padding: 6px 6px;
            background-color: #f1f5f9;
            border-radius: 6px;
            border: none;
        """)
        label_widget.setMinimumWidth(110)  # 保持标签宽度不变
        label_widget.setAlignment(Qt.AlignCenter)

        value_widget = QLabel(f"{value} {unit}")
        # 突出的数值显示 - 大字体，加粗
        value_widget.setStyleSheet(f"""
            color: #1f2937;
            font-size: 18px;
            font-weight: 700;
            min-height: 28px;
            padding: 6px 6px;
            text-align: center;
        """)
        value_widget.setAlignment(Qt.AlignCenter)
        value_widget.setMinimumWidth(155)  # 保持数值宽度不变

        item_layout.addWidget(label_widget)  # 标签靠左
        item_layout.addStretch()  # 中间拉伸空间，自动调整间距
        item_layout.addWidget(value_widget)  # 数值靠右

        self.data_layout.addLayout(item_layout)
    
    # 删除进度条方法，统一使用数据项显示


class FaultHistoryWindow(QDialog):
    """历史故障记录窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("历史故障记录")
        self.setGeometry(100, 100, 1000, 600)
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {PRIMARY_BG};
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("📋 历史故障记录")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
            margin-bottom: 10px;
        """)
        layout.addWidget(title_label)
        
        # 筛选区域
        filter_layout = QHBoxLayout()
        
        # 日期筛选
        date_label = QLabel("日期范围:")
        date_label.setStyleSheet(f"color: {TEXT_PRIMARY}; font-size: 16px;")
        filter_layout.addWidget(date_label)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setStyleSheet(f"""
            QDateEdit {{
                padding: 8px;
                border: 2px solid {ACCENT_COLOR};
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
            }}
        """)
        filter_layout.addWidget(self.start_date)
        
        filter_layout.addWidget(QLabel("至"))
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setStyleSheet(self.start_date.styleSheet())
        filter_layout.addWidget(self.end_date)
        
        # 故障类型筛选
        type_label = QLabel("故障类型:")
        type_label.setStyleSheet(f"color: {TEXT_PRIMARY}; font-size: 16px;")
        filter_layout.addWidget(type_label)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["全部", "外圈损伤", "内圈磨损", "保持架断裂", "内圈点蚀"])
        self.type_combo.setStyleSheet(f"""
            QComboBox {{
                padding: 8px;
                border: 2px solid {ACCENT_COLOR};
                border-radius: 5px;
                font-size: 14px;
                background-color: white;
                min-width: 120px;
            }}
        """)
        filter_layout.addWidget(self.type_combo)
        
        filter_layout.addStretch()
        
        # 搜索按钮
        search_btn = QPushButton("🔍 搜索")
        search_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
        """)
        search_btn.clicked.connect(self.search_records)
        filter_layout.addWidget(search_btn)
        
        layout.addLayout(filter_layout)
        
        # 表格
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels(["时间", "检测方法", "故障类型", "置信度", "状态", "详情"])
        
        # 设置表格样式
        self.table.setStyleSheet(f"""
            QTableWidget {{
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-size: 14px;
                gridline-color: #f0f0f0;
            }}
            QTableWidget::item {{
                padding: 12px;
                border-bottom: 1px solid #f0f0f0;
            }}
            QTableWidget::item:selected {{
                background-color: {SECONDARY_BG};
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
            }}
        """)
        
        # 设置表格属性
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.table)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        export_btn = QPushButton("📄 导出记录")
        export_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: #5dade2;
            }}
        """)
        button_layout.addWidget(export_btn)
        
        close_btn = QPushButton("❌ 关闭")
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {TEXT_SECONDARY};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 16px;
            }}
            QPushButton:hover {{
                background-color: #2c3e50;
            }}
        """)
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
    
    def load_sample_data(self):
        """加载示例数据"""
        sample_data = [
            ["2024-08-01 14:28:45", "深度学习监测", "外圈损伤", "94.7%", "报警", "高置信度检测"],
            ["2024-08-01 14:28:45", "故障判断", "外圈损伤", "86.3%", "报警", "多算法融合"],
            ["2024-08-01 11:45:22", "传统分类器", "内圈磨损", "68.5%", "警告", "SVM分类器"],
            ["2024-07-31 16:32:11", "深度学习监测", "保持架断裂", "82.1%", "报警", "ResNet-1D模型"],
            ["2024-07-30 09:15:37", "故障判断", "内圈点蚀", "73.8%", "警告", "特征分析"],
            ["2024-07-29 15:22:18", "深度学习监测", "外圈损伤", "91.2%", "报警", "CNN模型"],
            ["2024-07-28 10:45:33", "传统分类器", "内圈磨损", "65.4%", "警告", "随机森林"],
            ["2024-07-27 13:18:45", "故障判断", "保持架断裂", "78.9%", "报警", "频域分析"],
        ]
        
        self.table.setRowCount(len(sample_data))
        
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                
                # 设置状态列的颜色
                if col == 4:  # 状态列
                    if value == "报警":
                        item.setBackground(QPalette().color(QPalette.Base))
                        item.setForeground(QPalette().color(QPalette.Text))
                    elif value == "警告":
                        item.setBackground(QPalette().color(QPalette.Base))
                        item.setForeground(QPalette().color(QPalette.Text))
                
                self.table.setItem(row, col, item)
    
    def search_records(self):
        """搜索记录"""
        QMessageBox.information(self, "搜索", "搜索功能已触发！\n实际应用中会根据筛选条件查询数据库。")


class FaultAlarmSystem(QWidget):
    """故障异常报警系统主页面"""
    
    def __init__(self, db_manager=None):
        super().__init__()
        self.db_manager = db_manager
        self.current_status = "alarm"  # normal, warning, alarm
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        # 适应1280x900分辨率：减小边距以节省空间
        layout.setContentsMargins(15, 15, 15, 15)  # 减小边距
        layout.setSpacing(15)  # 减小间距以适应较小空间

        # 标题 - 适应小屏幕，减小字体
        title_label = QLabel("⚠️ 故障异常报警系统")
        title_label.setStyleSheet(f"""
            font-size: 20px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
            margin-bottom: 10px;
            padding: 8px 0px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 状态栏 - 单独置于页面上方，减少与下方数据的干扰
        self.create_status_bar(layout)

        # 添加分隔空间 - 减小以节省空间
        layout.addSpacing(10)

        # 监测卡片区域 - 增加分隔线或使用卡片式设计
        self.create_monitoring_cards(layout)

        # 添加间距，将按钮下移 - 减小间距
        layout.addSpacing(15)

        # 控制按钮区域 - 统一按钮风格和间距
        self.create_control_buttons(layout)

        layout.addStretch()

    def create_status_bar(self, parent_layout):
        """创建状态栏 - 单独置于页面上方的状态栏"""
        self.status_frame = QFrame()
        self.status_frame.setFixedHeight(80)  # 进一步减小高度以适应小屏幕
        self.update_status_bar()

        status_layout = QHBoxLayout(self.status_frame)
        status_layout.setContentsMargins(20, 10, 20, 10)  # 减小边距
        status_layout.setSpacing(15)  # 减小间距

        # 状态指示器
        indicator_layout = QHBoxLayout()
        indicator_layout.setSpacing(15)

        self.status_icon = QLabel("⚠️")
        self.status_icon.setStyleSheet("font-size: 28px;")  # 进一步减小图标以适应小屏幕
        indicator_layout.addWidget(self.status_icon)

        status_text_layout = QVBoxLayout()
        status_text_layout.setSpacing(5)

        self.status_text = QLabel("轴承故障报警")
        self.status_text.setStyleSheet(f"""
            font-size: 16px;
            font-weight: bold;
            color: {TEXT_PRIMARY};
        """)
        status_text_layout.addWidget(self.status_text)

        self.status_desc = QLabel("系统检测到异常情况，请立即检查！")
        self.status_desc.setStyleSheet(f"""
            font-size: 12px;
            color: {TEXT_SECONDARY};
        """)
        status_text_layout.addWidget(self.status_desc)

        indicator_layout.addLayout(status_text_layout)
        status_layout.addLayout(indicator_layout)

        status_layout.addStretch()

        # 设备信息和更新时间 - 右侧信息区
        info_layout = QVBoxLayout()
        info_layout.setAlignment(Qt.AlignRight)
        info_layout.setSpacing(5)

        device_label = QLabel("设备ID: BX-2037-08")
        device_label.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 13px;
            font-weight: 600;
        """)
        info_layout.addWidget(device_label)

        self.last_update = QLabel()
        self.last_update.setStyleSheet(f"""
            color: {TEXT_SECONDARY};
            font-size: 12px;
        """)
        info_layout.addWidget(self.last_update)

        status_layout.addLayout(info_layout)

        parent_layout.addWidget(self.status_frame)

        # 更新时间
        self.update_time()

    def update_status_bar(self):
        """更新状态栏样式 - 现代工业设计"""
        if self.current_status == "normal":
            border_color = "#10b981"
            bg_color = "qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #f0fdf4, stop: 1 #ecfdf5)"
        elif self.current_status == "warning":
            border_color = "#f59e0b"
            bg_color = "qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #fffbeb, stop: 1 #fef3c7)"
        else:  # alarm
            border_color = "#ef4444"
            bg_color = "qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #fef2f2, stop: 1 #fee2e2)"

        self.status_frame.setStyleSheet(f"""
            QFrame {{
                background: {bg_color};
                border: 2px solid {border_color};
                border-radius: 12px;
            }}
        """)

    def create_monitoring_cards(self, parent_layout):
        """创建监测卡片区域 - 仅在卡片区域添加滚动功能"""
        # 创建滚动区域 - 只允许纵向滚动
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用横向滚动条
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 只在需要时显示纵向滚动条
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f1f5f9;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #cbd5e1;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #94a3b8;
            }
        """)

        # 卡片容器 - 使用卡片式设计，适应小屏幕
        cards_container = QFrame()
        cards_container.setStyleSheet(f"""
            QFrame {{
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                margin: 2px;
                padding: 8px;
            }}
        """)

        cards_layout = QHBoxLayout(cards_container)
        cards_layout.setSpacing(6)  # 进一步减小卡片间距
        cards_layout.setContentsMargins(5, 8, 5, 8)  # 进一步减小容器边距
        cards_layout.setAlignment(Qt.AlignCenter)  # 确保卡片居中对齐

        # 故障判断卡片
        self.fault_card = StatusCard("故障判断", "", "alarm")
        self.fault_card.add_data_item("振动幅度", "8.7", "mm/s")
        self.fault_card.add_data_item("温度变化", "+12.5", "°C")
        self.fault_card.add_data_item("噪声水平", "78", "dB")
        self.fault_card.add_data_item("故障概率", "92%")
        self.fault_card.add_data_item("故障类型", "外圈损伤")
        self.fault_card.add_data_item("置信度", "86.3%")
        cards_layout.addWidget(self.fault_card)

        # 传统分类器监测卡片
        self.classical_card = StatusCard("传统分类器监测", "", "normal")
        self.classical_card.add_data_item("特征向量", "[0.45, 0.32]")
        self.classical_card.add_data_item("分类结果", "正常")
        self.classical_card.add_data_item("置信度", "76.2%")
        self.classical_card.add_data_item("异常概率", "24%")
        self.classical_card.add_data_item("使用模型", "SVM分类器")
        self.classical_card.add_data_item("更新时间", "14:28:42")
        cards_layout.addWidget(self.classical_card)

        # 深度学习监测卡片
        self.dl_card = StatusCard("深度学习监测", "", "alarm")
        self.dl_card.add_data_item("模型输出", "故障 (外圈)")
        self.dl_card.add_data_item("置信度", "94.7%")
        self.dl_card.add_data_item("特征相似度", "0.89")
        self.dl_card.add_data_item("故障概率", "95%")
        self.dl_card.add_data_item("使用模型", "ResNet-1D")
        self.dl_card.add_data_item("推理时间", "128 ms")
        cards_layout.addWidget(self.dl_card)

        # 将卡片容器设置到滚动区域
        scroll_area.setWidget(cards_container)

        # 将滚动区域添加到父布局
        parent_layout.addWidget(scroll_area)

    def create_control_buttons(self, parent_layout):
        """创建控制按钮区域 - 适应1280x900分辨率"""
        # 删除按钮区域标题以节省空间

        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)  # 减小按钮间距以适应小屏幕
        button_layout.setAlignment(Qt.AlignCenter)

        # 现代工业扁平风格按钮基础样式
        base_button_style = """
            QPushButton {{
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 16px;
                font-weight: 600;
                font-size: 13px;
                min-width: 120px;
                min-height: 42px;
            }}
            QPushButton:hover {{
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 9px 15px;
            }}
            QPushButton:pressed {{
                padding: 11px 17px;
                border: 1px solid rgba(0, 0, 0, 0.1);
            }}
        """

        # 现代工业配色方案
        refresh_color = "#3b82f6"
        refresh_hover = "#2563eb"
        history_color = "#06b6d4"
        history_hover = "#0891b2"

        # 刷新数据按钮
        refresh_btn = QPushButton("� 刷新数据")
        refresh_btn.setStyleSheet(base_button_style + f"""
            QPushButton {{
                background-color: {refresh_color};
            }}
            QPushButton:hover {{
                background-color: {refresh_hover};
            }}
            QPushButton:pressed {{
                background-color: #1d4ed8;
            }}
        """)
        refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(refresh_btn)

        # 历史故障记录按钮
        history_btn = QPushButton("📋 历史故障记录")
        history_btn.setStyleSheet(base_button_style + f"""
            QPushButton {{
                background-color: {history_color};
            }}
            QPushButton:hover {{
                background-color: {history_hover};
            }}
            QPushButton:pressed {{
                background-color: #0e7490;
            }}
        """)
        history_btn.clicked.connect(self.show_history)
        button_layout.addWidget(history_btn)

        # 消音报警按钮 - 统一红橙色调
        mute_btn = QPushButton("🔇 消音报警")
        mute_btn.setStyleSheet(base_button_style + f"""
            QPushButton {{
                background-color: #ef4444;
            }}
            QPushButton:hover {{
                background-color: #dc2626;
            }}
            QPushButton:pressed {{
                background-color: #b91c1c;
            }}
        """)
        mute_btn.clicked.connect(self.mute_alarm)
        button_layout.addWidget(mute_btn)

        # 生成报告按钮 - 现代绿色
        report_btn = QPushButton("📄 生成报告")
        report_btn.setStyleSheet(base_button_style + f"""
            QPushButton {{
                background-color: #10b981;
            }}
            QPushButton:hover {{
                background-color: #059669;
            }}
            QPushButton:pressed {{
                background-color: #047857;
            }}
        """)
        report_btn.clicked.connect(self.generate_report)
        button_layout.addWidget(report_btn)

        parent_layout.addLayout(button_layout)

    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新一次

        # 模拟数据更新定时器
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.simulate_data_update)
        self.data_timer.start(5000)  # 每5秒更新一次数据

    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.last_update.setText(f"最后更新: {current_time}")

    def simulate_data_update(self):
        """模拟数据更新"""
        # 随机改变系统状态
        statuses = ["normal", "warning", "alarm"]
        weights = [0.3, 0.3, 0.4]  # 报警状态概率更高
        self.current_status = random.choices(statuses, weights=weights)[0]

        # 更新状态栏
        if self.current_status == "normal":
            self.status_icon.setText("✅")
            self.status_text.setText("系统运行正常")
            self.status_desc.setText("所有监测指标正常，设备运行良好")
        elif self.current_status == "warning":
            self.status_icon.setText("⚠️")
            self.status_text.setText("系统警告")
            self.status_desc.setText("检测到轻微异常，建议关注")
        else:  # alarm
            self.status_icon.setText("🚨")
            self.status_text.setText("轴承故障报警")
            self.status_desc.setText("系统检测到异常情况，请立即检查！")

        self.update_status_bar()

        # 更新卡片状态
        if hasattr(self, 'fault_card'):
            self.fault_card.update_status(self.current_status)
        if hasattr(self, 'classical_card'):
            self.classical_card.update_status("normal" if random.random() > 0.3 else "warning")
        if hasattr(self, 'dl_card'):
            self.dl_card.update_status(self.current_status)

    def refresh_data(self):
        """刷新数据"""
        QMessageBox.information(self, "刷新数据", "数据已刷新！\n实际应用中会从传感器和数据库获取最新数据。")
        self.simulate_data_update()

    def show_history(self):
        """显示历史故障记录"""
        history_window = FaultHistoryWindow(self)
        history_window.exec_()

    def mute_alarm(self):
        """消音报警"""
        QMessageBox.information(self, "消音报警", "报警已消音！\n实际应用中会停止声音报警，但视觉报警仍然保持。")

    def generate_report(self):
        """生成报告"""
        QMessageBox.information(self, "生成报告", "报告生成功能已触发！\n实际应用中会生成详细的故障诊断报告。")
