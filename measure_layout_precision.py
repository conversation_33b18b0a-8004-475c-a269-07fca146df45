"""
精确测量故障异常报警页面卡片布局的对称性
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from ui.fault_alarm_system import FaultAlarmSystem, StatusCard

class PrecisionLayoutMeasurer(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("精确布局对称性测量器")
        self.setFixedSize(1000, 700)  # 模拟1000px工作区
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 10, 5, 10)
        
        # 标题
        title = QLabel("故障异常报警页面 - 精确对称性测量")
        title.setStyleSheet("font-size: 16px; font-weight: bold; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 创建故障报警系统
        self.fault_alarm = FaultAlarmSystem()
        layout.addWidget(self.fault_alarm)
        
        # 测量结果显示
        self.result_label = QLabel()
        self.result_label.setStyleSheet("""
            QLabel {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                padding: 10px;
                margin: 5px;
            }
        """)
        layout.addWidget(self.result_label)
        
        # 延迟测量
        QTimer.singleShot(1000, self.measure_precision)
    
    def measure_precision(self):
        """精确测量布局"""
        result_text = "=== 精确布局对称性测量报告 ===\n\n"
        
        # 获取所有卡片
        cards = []
        if hasattr(self.fault_alarm, 'fault_card'):
            cards.append(("故障判断卡片", self.fault_alarm.fault_card))
        if hasattr(self.fault_alarm, 'classical_card'):
            cards.append(("传统分类器卡片", self.fault_alarm.classical_card))
        if hasattr(self.fault_alarm, 'dl_card'):
            cards.append(("深度学习卡片", self.fault_alarm.dl_card))
        
        total_asymmetry = 0
        total_items = 0
        
        for card_name, card in cards:
            result_text += f"--- {card_name} ---\n"
            result_text += f"卡片尺寸: {card.width()} x {card.height()}px\n"
            result_text += f"卡片位置: ({card.x()}, {card.y()})\n\n"
            
            # 分析数据项
            if hasattr(card, 'data_layout'):
                data_layout = card.data_layout
                
                for i in range(data_layout.count()):
                    item = data_layout.itemAt(i)
                    if item and item.layout():
                        asymmetry = self.measure_data_item(item.layout(), i + 1, card, result_text)
                        if asymmetry is not None:
                            total_asymmetry += abs(asymmetry)
                            total_items += 1
            
            result_text += "\n"
        
        # 总体评估
        if total_items > 0:
            avg_asymmetry = total_asymmetry / total_items
            result_text += f"=== 总体对称性评估 ===\n"
            result_text += f"测量的数据项总数: {total_items}\n"
            result_text += f"平均不对称度: {avg_asymmetry:.1f}px\n"
            
            if avg_asymmetry <= 1:
                result_text += "✅ 布局对称性: 优秀\n"
            elif avg_asymmetry <= 3:
                result_text += "⚠️ 布局对称性: 良好，有轻微不对称\n"
            else:
                result_text += "❌ 布局对称性: 需要调整\n"
        
        self.result_label.setText(result_text)
        print(result_text)
        
        return result_text
    
    def measure_data_item(self, item_layout, item_num, card, result_text):
        """精确测量单个数据项的对称性"""
        label_widget = None
        value_widget = None
        
        # 查找标签和数值控件
        for i in range(item_layout.count()):
            item = item_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if hasattr(widget, 'text') and widget.text().strip():
                    if i == 0:  # 第一个是标签
                        label_widget = widget
                    elif i == 2:  # 第三个是数值（中间是拉伸空间）
                        value_widget = widget
        
        if label_widget and value_widget:
            # 获取卡片的实际内容区域
            card_content_rect = card.contentsRect()
            card_width = card_content_rect.width()
            
            # 计算标签和数值的绝对位置（相对于卡片）
            label_global_pos = label_widget.mapTo(card, label_widget.rect().topLeft())
            value_global_pos = value_widget.mapTo(card, value_widget.rect().topLeft())
            
            # 计算边距
            label_left_margin = label_global_pos.x()
            value_right_margin = card_width - (value_global_pos.x() + value_widget.width())
            
            # 计算不对称度
            asymmetry = label_left_margin - value_right_margin
            
            # 添加到结果文本
            global result_text_global
            result_text_global = f"  数据项 {item_num}:\n"
            result_text_global += f"    标签: '{label_widget.text()}' 宽度={label_widget.width()}px\n"
            result_text_global += f"    标签位置: 全局({label_global_pos.x()}, {label_global_pos.y()})\n"
            result_text_global += f"    标签左边距: {label_left_margin}px\n"
            result_text_global += f"    数值: '{value_widget.text()}' 宽度={value_widget.width()}px\n"
            result_text_global += f"    数值位置: 全局({value_global_pos.x()}, {value_global_pos.y()})\n"
            result_text_global += f"    数值右边距: {value_right_margin}px\n"
            result_text_global += f"    不对称度: {asymmetry:+.1f}px "
            
            if abs(asymmetry) <= 1:
                result_text_global += "✅ 对称\n"
            elif abs(asymmetry) <= 3:
                result_text_global += "⚠️ 轻微不对称\n"
            else:
                result_text_global += "❌ 明显不对称\n"
            
            print(result_text_global)
            
            return asymmetry
        
        return None

def main():
    app = QApplication(sys.argv)
    
    measurer = PrecisionLayoutMeasurer()
    measurer.show()
    
    print("精确布局对称性测量器启动...")
    print("正在分析故障异常报警页面的卡片布局...")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n测量结束")
        sys.exit(0)

if __name__ == "__main__":
    main()
