"""
真实主程序环境下的故障异常报警页面布局测试
模拟导航栏占用280px宽度的情况
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                             QVBoxLayout, QLabel, QSplitter)
from PyQt5.QtCore import Qt, QTimer
from ui.fault_alarm_system import FaultAlarmSystem, StatusCard
from ui.styles import PRIMARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY

class MockNavigationBar(QWidget):
    """模拟导航栏"""
    def __init__(self):
        super().__init__()
        self.setFixedWidth(280)  # 与主程序相同的导航栏宽度
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PRIMARY_BG};
                border-right: 2px solid #cccccc;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        title = QLabel("导航栏 (280px)")
        title.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: 16px;
                font-weight: bold;
                padding: 10px;
                background-color: {ACCENT_COLOR};
                color: white;
                border-radius: 5px;
            }}
        """)
        layout.addWidget(title)
        layout.addStretch()

class RealLayoutTester(QMainWindow):
    """真实布局测试器"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("真实主程序环境 - 故障异常报警页面布局测试")
        self.setGeometry(100, 100, 1280, 900)  # 与主程序相同的窗口大小
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器（模拟主程序的分割器）
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                width: 3px;
            }
        """)
        
        # 左侧导航栏（280px）
        nav_bar = MockNavigationBar()
        splitter.addWidget(nav_bar)
        
        # 右侧工作区容器
        workspace_container = QWidget()
        workspace_layout = QVBoxLayout(workspace_container)
        workspace_layout.setContentsMargins(0, 0, 0, 0)
        workspace_layout.setSpacing(0)
        
        # 顶部区域（模拟logo区域，80px高度）
        top_area = QWidget()
        top_area.setFixedHeight(80)
        top_area.setStyleSheet(f"""
            QWidget {{
                background-color: {PRIMARY_BG};
                border-bottom: 1px solid #cccccc;
            }}
        """)
        top_layout = QHBoxLayout(top_area)
        top_layout.setContentsMargins(20, 10, 20, 10)
        
        top_label = QLabel("工作区顶部 (1000px宽度)")
        top_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: 16px;
                font-weight: bold;
            }}
        """)
        top_layout.addWidget(top_label)
        top_layout.addStretch()
        
        workspace_layout.addWidget(top_area)
        
        # 故障异常报警系统（实际工作区）
        self.fault_alarm = FaultAlarmSystem()
        workspace_layout.addWidget(self.fault_alarm)
        
        splitter.addWidget(workspace_container)
        
        # 设置分割器比例（与主程序相同）
        splitter.setSizes([280, 1000])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
        
        # 设置定时器来分析布局
        QTimer.singleShot(1000, self.analyze_real_layout)
    
    def analyze_real_layout(self):
        """分析真实环境下的布局"""
        print("\n=== 真实主程序环境布局分析 ===")
        print(f"窗口总宽度: {self.width()}px")
        print(f"导航栏宽度: 280px")
        print(f"工作区可用宽度: {self.width() - 280}px")
        
        # 获取工作区实际宽度
        workspace_widget = self.fault_alarm.parent()
        if workspace_widget:
            actual_width = workspace_widget.width()
            print(f"工作区实际宽度: {actual_width}px")
        
        # 分析卡片布局
        cards = []
        if hasattr(self.fault_alarm, 'fault_card'):
            cards.append(("故障判断卡片", self.fault_alarm.fault_card))
        if hasattr(self.fault_alarm, 'classical_card'):
            cards.append(("传统分类器卡片", self.fault_alarm.classical_card))
        if hasattr(self.fault_alarm, 'dl_card'):
            cards.append(("深度学习卡片", self.fault_alarm.dl_card))
        
        total_cards_width = 0
        for card_name, card in cards:
            card_rect = card.geometry()
            card_width = card_rect.width()
            total_cards_width += card_width
            print(f"{card_name}: 宽度={card_width}px, 位置=({card_rect.x()}, {card_rect.y()})")
            
            # 检查是否超出工作区边界
            if card_rect.x() + card_width > actual_width:
                overflow = (card_rect.x() + card_width) - actual_width
                print(f"  ⚠️ 超出工作区右边界 {overflow}px")
            else:
                print(f"  ✅ 在工作区边界内")
        
        print(f"\n三个卡片总宽度: {total_cards_width}px")
        print(f"工作区可用宽度: {actual_width}px")
        
        if total_cards_width > actual_width:
            print(f"❌ 卡片总宽度超出工作区 {total_cards_width - actual_width}px")
        else:
            print(f"✅ 卡片适合工作区，剩余空间 {actual_width - total_cards_width}px")

def main():
    app = QApplication(sys.argv)
    
    # 创建真实环境测试器
    tester = RealLayoutTester()
    tester.show()
    
    print("真实主程序环境布局测试器已启动...")
    print("模拟导航栏280px + 工作区1000px的真实环境")
    print("分析报告将在1秒后显示")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n测试结束")
        sys.exit(0)

if __name__ == "__main__":
    main()
