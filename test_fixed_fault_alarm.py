#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的故障报警系统界面
验证文字框遮挡和进度条对齐问题的修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from ui.fault_alarm_system import FaultAlarmSystem

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("故障报警系统界面修复测试")
        self.setGeometry(100, 100, 1280, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加故障报警系统
        self.fault_alarm = FaultAlarmSystem()
        layout.addWidget(self.fault_alarm)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 创建并显示测试窗口
    window = TestWindow()
    window.show()
    
    print("故障报警系统界面修复测试")
    print("=" * 100)
    print("最新修复内容（卡片内容居中对齐）：")
    print("1. 删除'实时监测数据'和'操作控制'标签")
    print("2. StatusCard响应式尺寸：最小300px宽，520px高，水平可扩展")
    print("3. 完全删除进度条功能和相关代码")
    print("4. 将进度条替换为普通数据项显示")
    print("5. 标签和数值都居中对齐")
    print("6. 标签宽度：120px，字体大小：16px，居中显示")
    print("7. 数值宽度：120px，居中对齐")
    print("8. 数据项布局：左拉伸-标签-中拉伸-数值-右拉伸")
    print("9. 移除整页滚动，滚动条仅在监测卡片区域")
    print("10. 标题和状态栏固定在顶部")
    print("11. 控制按钮固定在底部")
    print("12. 三个卡片平均分配可用宽度")
    print("13. 禁用横向滚动条，只保留纵向滚动")
    print("=" * 100)
    print("重点检查：")
    print("- 卡片内容是否整体居中显示（不再偏右）")
    print("- 标签文字是否居中对齐")
    print("- 数值是否居中对齐")
    print("- 标签和数值在卡片中是否视觉平衡")
    print("- 滚动条是否只在监测卡片区域出现")
    print("- 三个卡片是否平均分配宽度")
    print("- 卡片是否随窗口宽度缩放")
    print("- 标题和状态栏是否固定在顶部不滚动")
    print("- 控制按钮是否固定在底部不滚动")
    print("- 整体布局是否协调美观")
    
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
