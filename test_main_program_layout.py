#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序布局中的故障报警系统
模拟真实的主程序环境，包含导航栏占用的宽度
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                             QVBoxLayout, QLabel, QFrame, QSplitter)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from ui.fault_alarm_system import FaultAlarmSystem
from ui.styles import PRIMARY_BG, TEXT_PRIMARY

class MockNavigationBar(QWidget):
    """模拟导航栏"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化导航栏"""
        self.setFixedWidth(280)  # 与主程序相同的导航栏宽度
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PRIMARY_BG};
                border-right: 2px solid #cccccc;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 20, 10, 20)
        
        # 模拟导航项目
        nav_items = [
            "🏠 仪表盘",
            "📁 文件选择", 
            "📊 特征提取与分析",
            "🔧 故障判断",
            "🔍 经典分类器监测",
            "🧠 深度学习监测",
            "⚠️ 故障异常报警",  # 当前页面
            "📋 检测报告生成",
            "⚙️ 系统设置"
        ]
        
        for i, item in enumerate(nav_items):
            label = QLabel(item)
            if i == 6:  # 当前页面高亮
                label.setStyleSheet(f"""
                    QLabel {{
                        background-color: #0066cc;
                        color: white;
                        padding: 12px;
                        border-radius: 8px;
                        font-weight: bold;
                        font-size: 14px;
                    }}
                """)
            else:
                label.setStyleSheet(f"""
                    QLabel {{
                        color: {TEXT_PRIMARY};
                        padding: 12px;
                        font-size: 14px;
                    }}
                """)
            layout.addWidget(label)
        
        layout.addStretch()

class MockTopArea(QWidget):
    """模拟顶部区域"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化顶部区域"""
        self.setFixedHeight(80)
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {PRIMARY_BG};
                border-left: 2px solid #cccccc;
                border-bottom: 1px solid #cccccc;
            }}
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # Logo和标题
        title_label = QLabel("地面数据分析决策系统")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: 18px;
                font-weight: bold;
            }}
        """)
        layout.addWidget(title_label)
        layout.addStretch()
        
        # 系统信息
        info_label = QLabel("主程序布局测试")
        info_label.setStyleSheet(f"""
            QLabel {{
                color: {TEXT_PRIMARY};
                font-size: 12px;
            }}
        """)
        layout.addWidget(info_label)

class MainProgramLayoutTest(QMainWindow):
    """主程序布局测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.show_test_info()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("主程序布局测试 - 故障报警系统")
        self.setGeometry(50, 50, 1280, 930)  # 与主程序相同的窗口大小
        self.setFixedSize(1280, 930)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #cccccc;
                width: 3px;
            }
            QSplitter::handle:hover {
                background-color: #0066cc;
            }
        """)
        
        # 左侧导航栏（模拟）
        self.navigation = MockNavigationBar()
        splitter.addWidget(self.navigation)
        
        # 右侧工作区
        workspace_container = QWidget()
        workspace_layout = QVBoxLayout(workspace_container)
        workspace_layout.setContentsMargins(0, 0, 0, 0)
        workspace_layout.setSpacing(0)
        
        # 顶部区域（模拟）
        top_area = MockTopArea()
        workspace_layout.addWidget(top_area)
        
        # 添加故障报警系统
        self.fault_alarm = FaultAlarmSystem()
        workspace_layout.addWidget(self.fault_alarm)
        
        splitter.addWidget(workspace_container)
        
        # 设置分割器比例（与主程序相同）
        splitter.setSizes([280, 1000])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
    
    def show_test_info(self):
        """显示测试信息"""
        print("主程序布局测试")
        print("=" * 80)
        print("测试环境：")
        print("- 总窗口宽度：1280px")
        print("- 导航栏宽度：280px")
        print("- 工作区宽度：约1000px")
        print("- 顶部区域高度：80px")
        print("=" * 80)
        print("优化内容：")
        print("1. 卡片最小宽度：280px（原300px）")
        print("2. 卡片最大宽度：360px（调整避免内容超出）")
        print("3. 标签宽度：110px（确保不超出卡片边界）")
        print("4. 数值宽度：130px（平衡显示完整性和边界控制）")
        print("5. 卡片间距：8px（原10px）")
        print("6. 容器边距：8px（原10px）")
        print("7. 数据区域边距：10px（减少边距给内容更多空间）")
        print("8. 数据项边距：5px（减少边距避免超出）")
        print("=" * 80)
        print("检查要点：")
        print("- 三个卡片是否在工作区内合理分布")
        print("- 右边内容是否不再超出卡片边界")
        print("- 左边空隙是否减少，布局更平衡")
        print("- 数值是否完整显示且不超出（如'SVM分类器'等）")
        print("- 标签和数值宽度是否协调（110px vs 130px）")
        print("- 内容是否在卡片内真正居中")
        print("- 边距设置是否合理")
        print("- 整体布局是否协调美观")
        print("- 与主程序环境的一致性")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("主程序布局测试")
    app.setApplicationVersion("1.0")
    
    # 创建并显示主窗口
    window = MainProgramLayoutTest()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
