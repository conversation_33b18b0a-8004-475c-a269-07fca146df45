"""
快速布局测试 - 验证在1000px工作区宽度下的卡片布局
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QHBoxLayout, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt, QTimer
from ui.fault_alarm_system import StatusCard

class QuickLayoutTest(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("1000px工作区布局测试")
        self.setFixedSize(1000, 600)  # 模拟1000px工作区宽度
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 10, 5, 10)
        layout.setSpacing(10)
        
        # 标题
        title = QLabel("1000px工作区宽度测试 - 三个卡片布局")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #333; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 卡片容器
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(6)
        cards_layout.setContentsMargins(5, 8, 5, 8)
        cards_layout.setAlignment(Qt.AlignCenter)
        
        # 创建三个测试卡片
        self.cards = []
        card_names = ["卡片1", "卡片2", "卡片3"]
        
        for i, name in enumerate(card_names):
            card = StatusCard(name, "", "normal")
            card.add_data_item("测试标签", "测试数值")
            card.add_data_item("振动幅度", "8.7", "mm/s")
            card.add_data_item("温度变化", "+12.5", "°C")
            self.cards.append(card)
            cards_layout.addWidget(card)
        
        layout.addLayout(cards_layout)
        
        # 信息显示
        self.info_label = QLabel()
        self.info_label.setStyleSheet("font-size: 12px; color: #666; padding: 10px;")
        layout.addWidget(self.info_label)
        
        layout.addStretch()
        
        # 延迟分析
        QTimer.singleShot(500, self.analyze_layout)
    
    def analyze_layout(self):
        """分析布局"""
        window_width = self.width()
        total_cards_width = 0
        
        info_text = f"工作区宽度: {window_width}px\n\n"
        
        for i, card in enumerate(self.cards):
            card_rect = card.geometry()
            card_width = card_rect.width()
            total_cards_width += card_width
            
            info_text += f"卡片{i+1}: 宽度={card_width}px, 位置=({card_rect.x()}, {card_rect.y()})\n"
            
            # 检查是否超出边界
            if card_rect.x() + card_width > window_width:
                overflow = (card_rect.x() + card_width) - window_width
                info_text += f"  ⚠️ 超出右边界 {overflow}px\n"
            else:
                info_text += f"  ✅ 在边界内\n"
        
        info_text += f"\n三个卡片总宽度: {total_cards_width}px\n"
        
        if total_cards_width > window_width - 20:  # 考虑边距
            info_text += f"❌ 卡片可能超出工作区\n"
        else:
            info_text += f"✅ 卡片适合工作区\n"
        
        # 检查单个卡片内部布局
        info_text += "\n=== 卡片内部布局检查 ===\n"
        card = self.cards[0]  # 检查第一个卡片
        
        # 查找标签和数值控件
        def find_widgets(widget):
            labels = []
            for child in widget.findChildren(QLabel):
                if hasattr(child, 'text') and child.text():
                    text = child.text().strip()
                    if text and text not in ["卡片1", "运行正常"]:  # 排除标题和状态
                        labels.append((text, child))
            return labels
        
        widgets = find_widgets(card)
        if len(widgets) >= 2:
            # 假设第一个是标签，第二个是数值
            label_text, label_widget = widgets[0]
            value_text, value_widget = widgets[1]
            
            card_width = card.width()
            label_x = label_widget.x()
            value_x = value_widget.x()
            value_width = value_widget.width()
            
            left_margin = label_x
            right_margin = card_width - (value_x + value_width)
            
            info_text += f"标签 '{label_text}': x={label_x}, 距左边={left_margin}px\n"
            info_text += f"数值 '{value_text}': x={value_x}, 宽度={value_width}, 距右边={right_margin}px\n"
            
            if abs(left_margin - right_margin) <= 2:
                info_text += "✅ 左右边距对称\n"
            else:
                info_text += f"⚠️ 左右边距不对称 (差值: {abs(left_margin - right_margin)}px)\n"
        
        self.info_label.setText(info_text)
        print(info_text)

def main():
    app = QApplication(sys.argv)
    
    test = QuickLayoutTest()
    test.show()
    
    print("1000px工作区布局测试启动...")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n测试结束")
        sys.exit(0)

if __name__ == "__main__":
    main()
