"""
验证故障异常报警页面卡片的对称性
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt, QTimer
from ui.fault_alarm_system import StatusCard

class SymmetryVerifier(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("卡片对称性验证")
        self.setFixedSize(800, 600)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title = QLabel("卡片对称性验证测试")
        title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 创建测试卡片
        self.test_card = StatusCard("测试卡片", "", "normal")
        self.test_card.add_data_item("振动幅度", "8.7", "mm/s")
        self.test_card.add_data_item("温度变化", "+12.5", "°C")
        self.test_card.add_data_item("噪声水平", "78", "dB")
        self.test_card.add_data_item("故障概率", "92%")
        self.test_card.add_data_item("故障类型", "外圈损伤")
        self.test_card.add_data_item("置信度", "86.3%")
        
        layout.addWidget(self.test_card, 0, Qt.AlignCenter)
        
        # 结果显示
        self.result_label = QLabel()
        self.result_label.setStyleSheet("""
            QLabel {
                font-family: 'Consolas', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 15px;
                border-radius: 5px;
            }
        """)
        layout.addWidget(self.result_label)
        
        # 延迟测量
        QTimer.singleShot(500, self.verify_symmetry)
    
    def verify_symmetry(self):
        """验证对称性"""
        result = "=== 卡片对称性验证结果 ===\n\n"
        
        card = self.test_card
        card_width = card.width()
        result += f"卡片宽度: {card_width}px\n\n"
        
        # 查找所有标签和数值控件
        all_labels = card.findChildren(QLabel)
        data_items = []
        
        # 过滤出数据项的标签和数值
        for label in all_labels:
            text = label.text().strip()
            if text and text not in ["测试卡片", "运行正常"] and ":" not in text:
                # 获取父布局来判断是标签还是数值
                parent_layout = label.parent()
                if parent_layout:
                    # 通过位置判断是标签还是数值
                    siblings = parent_layout.findChildren(QLabel)
                    if len(siblings) >= 2:
                        # 按x坐标排序
                        siblings.sort(key=lambda w: w.x())
                        if label == siblings[0]:
                            # 这是标签，找对应的数值
                            if len(siblings) > 1:
                                value_widget = siblings[1]
                                data_items.append((label, value_widget))
        
        total_asymmetry = 0
        valid_items = 0
        
        for i, (label_widget, value_widget) in enumerate(data_items[:6]):  # 只取前6个
            # 计算边距
            label_left = label_widget.x()
            value_right = card_width - (value_widget.x() + value_widget.width())
            asymmetry = label_left - value_right
            
            result += f"数据项 {i+1}:\n"
            result += f"  标签: '{label_widget.text()}'\n"
            result += f"  标签左边距: {label_left}px\n"
            result += f"  数值: '{value_widget.text()}'\n"
            result += f"  数值右边距: {value_right}px\n"
            result += f"  不对称度: {asymmetry:+.0f}px "
            
            if abs(asymmetry) <= 1:
                result += "✅ 对称\n"
            elif abs(asymmetry) <= 2:
                result += "⚠️ 基本对称\n"
            else:
                result += "❌ 不对称\n"
            
            result += "\n"
            
            total_asymmetry += abs(asymmetry)
            valid_items += 1
        
        if valid_items > 0:
            avg_asymmetry = total_asymmetry / valid_items
            result += f"=== 总体评估 ===\n"
            result += f"平均不对称度: {avg_asymmetry:.1f}px\n"
            
            if avg_asymmetry <= 1:
                result += "✅ 整体对称性: 优秀\n"
            elif avg_asymmetry <= 2:
                result += "⚠️ 整体对称性: 良好\n"
            else:
                result += "❌ 整体对称性: 需要改进\n"
        
        self.result_label.setText(result)
        print(result)

def main():
    app = QApplication(sys.argv)
    
    verifier = SymmetryVerifier()
    verifier.show()
    
    print("卡片对称性验证器启动...")
    
    try:
        sys.exit(app.exec_())
    except KeyboardInterrupt:
        print("\n验证结束")
        sys.exit(0)

if __name__ == "__main__":
    main()
